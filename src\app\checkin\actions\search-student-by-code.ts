'use server';

import { z } from 'zod';
import { createTenantServerClient } from '@/services/supabase/server';
import { getTenantSlug } from '@/services/tenant/';
import { SupabaseErrorHandler } from '@/utils/supabase-error-handler';
import { getStudentMembershipForCheckIn } from './get-student-active-plans';

export interface StudentCheckInData {
  student: {
    id: string;
    name: string;
    email?: string;
    avatar?: string;
    code?: string;
    membership?: {
      type?: string;
      status?: 'active' | 'canceled' | 'past_due' | 'unpaid' | string;
      expiresAt?: string;
    };
  };
  // Manter assinatura compatível com interfaces existentes
  availableClasses: any[];
}

interface RequestData {
  code: string;
}

const schema = z.object({
  code: z.string().trim().min(1, 'Código obrigatório'),
});

export async function findStudentByCheckInCode(
  payload: unknown,
): Promise<
  | { success: true; data: StudentCheckInData }
  | { success: false; error: string }
> {
  const parsed = schema.safeParse(payload);
  if (!parsed.success) {
    return { success: false, error: 'Parâmetros inválidos' };
  }

  const { code } = parsed.data as RequestData;

  try {
    console.log('Iniciando busca por código de check-in:', code);
    
    const supabase = await createTenantServerClient();
    console.log('Cliente Supabase criado com sucesso');
    
    const tenantSlug = await getTenantSlug();
    console.log('Tenant slug obtido:', tenantSlug);

    if (!tenantSlug) {
      console.error('Tenant slug não encontrado');
      return { success: false, error: 'Tenant não encontrado' };
    }

    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single();

    if (tenantError) {
      console.error('Erro ao buscar tenant:', tenantError);
      return { success: false, error: 'Erro ao buscar configurações do tenant' };
    }

    if (!tenant) {
      console.error('Tenant não encontrado para slug:', tenantSlug);
      return { success: false, error: 'Tenant inválido' };
    }

    console.log('Tenant encontrado:', tenant.id);

    // Buscar aluno pelo código de check-in
    const { data: student, error: studentError } = await supabase
      .from('students')
      .select(
        `id,
         check_in_code,
         users:users!students_user_id_fkey!inner(full_name, email, avatar_url)`
      )
      .eq('tenant_id', tenant.id)
      .eq('check_in_code', code)
      .limit(1)
      .maybeSingle();

    if (studentError) {
      console.error('Erro ao buscar aluno por código:', studentError);
      return { success: false, error: 'Erro ao buscar aluno' };
    }

    if (!student) {
      console.log('Aluno não encontrado para código:', code);
      return { success: false, error: 'Aluno não encontrado' };
    }

    console.log('Aluno encontrado:', student.id);

    const user = student.users || {};

    // Buscar membership ativa do aluno
    const membershipInfo = await getStudentMembershipForCheckIn(
      student.id,
      tenant.id,
      supabase
    );

    // Buscar IDs de turmas em que o aluno está matriculado (status ativo)
    const { data: enrollments, error: enrollErr } = await supabase
      .from('class_group_enrollments')
      .select('class_group_id')
      .eq('tenant_id', tenant.id)
      .eq('student_id', student.id)
      .eq('status', 'active');

    if (enrollErr) {
      console.error('Erro ao buscar matrículas do aluno:', enrollErr);
    }

    const enrolledGroupIds = (enrollments ?? []).map((e: any) => e.class_group_id);

    // ----------------------------------------------------------------
    // Buscar aulas disponíveis HOJE apenas das turmas do aluno ou aulas livres
    // ----------------------------------------------------------------
    const now = new Date();
    const dayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const dayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    const { data: classesToday, error: classesError } = await supabase
      .from('classes')
      .select(`
        id,
        name,
        start_time,
        end_time,
        status,
        max_capacity,
        class_group_id,
        instructor:users!instructor_id(full_name),
        branch:branches!inner(name)
      `)
      .eq('tenant_id', tenant.id)
      .gte('start_time', dayStart.toISOString())
      .lte('start_time', dayEnd.toISOString())
      .in('status', ['scheduled', 'ongoing'])
      .order('start_time', { ascending: true });

    if (classesError) {
      console.error('Erro ao buscar aulas de hoje:', classesError);
    }

    // Filtrar apenas aulas da turma do aluno ou aulas livres (class_group_id null)
    const filteredClasses = (classesToday || []).filter((cls: any) => {
      return cls.class_group_id === null || enrolledGroupIds.includes(cls.class_group_id);
    });

    // Buscar contagem de presença por aula (no mesmo dia)
    const attendanceCountsMap: Record<string, number> = {};
    if (filteredClasses && filteredClasses.length > 0) {
      await Promise.all(
        filteredClasses.map(async (cls: any) => {
          const { count } = await supabase
            .from('attendance')
            .select('*', { count: 'exact', head: true })
            .eq('tenant_id', tenant.id)
            .eq('class_id', cls.id)
            .gte('checked_in_at', dayStart.toISOString())
            .lte('checked_in_at', dayEnd.toISOString());

          attendanceCountsMap[cls.id] = count || 0;
        }),
      );
    }

    // Formatar horário em 'HH:MM'
    const formatTime = (dateString: string) => {
      const date = new Date(dateString);
      return date.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
      });
    };

    const availableClassesFormatted = filteredClasses.map((cls: any) => {
      // Converter status para os valores usados no front-end
      let status: 'upcoming' | 'in-progress' | 'ended' = 'upcoming';
      if (cls.status === 'ongoing') status = 'in-progress';
      if (cls.status === 'completed' || cls.status === 'cancelled') status = 'ended';

      return {
        id: cls.id,
        name: cls.name,
        instructor: cls.instructor?.full_name ?? 'Instrutor',
        time: `${formatTime(cls.start_time)} - ${formatTime(cls.end_time)}`,
        location: cls.branch?.name ?? 'Academia',
        capacity: cls.max_capacity ?? 0,
        enrolled: attendanceCountsMap[cls.id] || 0,
        status,
      };
    });

    const formatted: StudentCheckInData = {
      student: {
        id: student.id,
        name: user.full_name ?? 'Aluno',
        email: user.email ?? undefined,
        avatar: user.avatar_url ?? undefined,
        code: student.check_in_code ?? undefined,
        membership: membershipInfo || {
          type: 'Sem plano',
          status: 'inactive',
          expiresAt: undefined,
        },
      },
      availableClasses: availableClassesFormatted,
    };

    console.log('Busca concluída com sucesso');
    return { success: true, data: formatted };
  } catch (err) {
    return SupabaseErrorHandler.createServerActionErrorResponse(
      err, 
      'findStudentByCheckInCode',
      'Erro ao buscar aluno por código'
    );
  }
} 